#!/usr/bin/env python3
"""
測試資料庫模組
"""

import sys
import tempfile
from pathlib import Path

import pytest
from sqlmodel import Session, select

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent.parent))

from when_did_i_take.db import get_engine, get_session, create_tables, get_database_info
from when_did_i_take.models import User, Pharmaceutical, Take, FormulationType


class TestDatabaseModule:
    """測試資料庫模組"""

    def test_get_engine(self):
        """測試取得資料庫引擎"""
        engine = get_engine()
        assert engine is not None
        assert hasattr(engine, 'url')

    def test_get_session(self):
        """測試建立 session"""
        session = get_session()
        assert isinstance(session, Session)
        session.close()

    def test_get_database_info(self):
        """測試取得資料庫資訊"""
        info = get_database_info()
        
        assert 'engine' in info
        assert 'database_type' in info
        assert 'using_default' in info
        assert 'url' in info
        
        assert info['database_type'] in ['sqlite', 'postgresql']
        assert isinstance(info['using_default'], bool)

    def test_create_tables(self):
        """測試建立資料表"""
        # 這個測試主要確保函數可以執行而不拋出錯誤
        try:
            create_tables()
        except Exception as e:
            pytest.fail(f"create_tables() 拋出了意外的錯誤: {e}")

    def test_session_context_manager(self):
        """測試 session 作為 context manager"""
        with get_session() as session:
            assert isinstance(session, Session)
            # 測試基本查詢
            users = session.exec(select(User)).all()
            assert isinstance(users, list)

    def test_database_operations(self):
        """測試基本的資料庫操作"""
        with get_session() as session:
            # 建立測試使用者
            test_user = User(
                name="測試使用者",
                email="<EMAIL>"
            )
            session.add(test_user)
            session.commit()
            session.refresh(test_user)
            
            assert test_user.id is not None
            assert test_user.name == "測試使用者"
            assert test_user.email == "<EMAIL>"
            
            # 建立測試藥物
            test_pharmaceutical = Pharmaceutical(
                name="測試藥物",
                formulation_type=FormulationType.PILL,
                dosage_per_unit=10.5,
                unit="mg",
                description="測試用藥物"
            )
            session.add(test_pharmaceutical)
            session.commit()
            session.refresh(test_pharmaceutical)
            
            assert test_pharmaceutical.id is not None
            assert test_pharmaceutical.name == "測試藥物"
            assert test_pharmaceutical.formulation_type == FormulationType.PILL
            
            # 清理測試資料
            session.delete(test_user)
            session.delete(test_pharmaceutical)
            session.commit()

    def test_foreign_key_relationships(self):
        """測試外鍵關聯"""
        with get_session() as session:
            # 建立測試資料
            test_user = User(
                name="關聯測試使用者",
                email="<EMAIL>"
            )
            session.add(test_user)
            session.commit()
            session.refresh(test_user)
            
            test_pharmaceutical = Pharmaceutical(
                name="關聯測試藥物",
                formulation_type=FormulationType.CAPSULE,
                dosage_per_unit=25.0,
                unit="mg"
            )
            session.add(test_pharmaceutical)
            session.commit()
            session.refresh(test_pharmaceutical)
            
            # 建立服藥記錄
            from datetime import datetime
            test_take = Take(
                user_id=test_user.id,
                pharmaceutical_id=test_pharmaceutical.id,
                quantity=1.0,
                taken_at=datetime.now(),
                notes="測試服藥記錄"
            )
            session.add(test_take)
            session.commit()
            session.refresh(test_take)
            
            # 測試關聯
            assert test_take.user_id == test_user.id
            assert test_take.pharmaceutical_id == test_pharmaceutical.id
            assert test_take.user.name == "關聯測試使用者"
            assert test_take.pharmaceutical.name == "關聯測試藥物"
            
            # 清理測試資料
            session.delete(test_take)
            session.delete(test_user)
            session.delete(test_pharmaceutical)
            session.commit()

    def test_unique_constraint(self):
        """測試唯一性約束"""
        with get_session() as session:
            # 建立第一個藥物
            pharmaceutical1 = Pharmaceutical(
                name="唯一性測試藥物",
                formulation_type=FormulationType.PILL,
                dosage_per_unit=10.0,
                unit="mg"
            )
            session.add(pharmaceutical1)
            session.commit()
            
            # 嘗試建立同名藥物（應該失敗）
            pharmaceutical2 = Pharmaceutical(
                name="唯一性測試藥物",  # 相同名稱
                formulation_type=FormulationType.CAPSULE,
                dosage_per_unit=20.0,
                unit="mg"
            )
            session.add(pharmaceutical2)
            
            with pytest.raises(Exception):  # 應該拋出唯一性約束錯誤
                session.commit()
            
            # 清理
            session.rollback()
            session.delete(pharmaceutical1)
            session.commit()
