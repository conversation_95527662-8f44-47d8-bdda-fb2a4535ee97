"""
測試認證功能
"""
import requests
import json

# API 基礎 URL
BASE_URL = "http://localhost:8001"

def test_login():
    """測試登入功能"""
    print("=== 測試登入功能 ===")
    
    # 測試成功登入（使用者 ID 1，密碼 password123）
    login_data = {
        "user_id": 1,
        "password": "password123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    print(f"登入請求狀態碼: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"登入成功！")
        print(f"Access Token: {result['access_token'][:50]}...")
        print(f"Token Type: {result['token_type']}")
        print(f"Expires In: {result['expires_in']} 秒")
        return result['access_token']
    else:
        print(f"登入失敗: {response.text}")
        return None

def test_login_failure():
    """測試登入失敗"""
    print("\n=== 測試登入失敗 ===")
    
    # 測試錯誤密碼
    login_data = {
        "user_id": 1,
        "password": "wrong_password"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    print(f"錯誤密碼登入狀態碼: {response.status_code}")
    print(f"錯誤訊息: {response.json()}")

def test_get_me(token):
    """測試取得使用者資訊"""
    print("\n=== 測試取得使用者資訊 ===")
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    print(f"取得使用者資訊狀態碼: {response.status_code}")
    
    if response.status_code == 200:
        user_info = response.json()
        print(f"使用者資訊: {user_info}")
    else:
        print(f"取得使用者資訊失敗: {response.text}")

def test_graphql_without_auth():
    """測試未認證的 GraphQL 請求"""
    print("\n=== 測試未認證的 GraphQL 請求 ===")
    
    query = """
    query {
        getUsers {
            id
            name
            email
        }
    }
    """
    
    response = requests.post(
        f"{BASE_URL}/graphql",
        json={"query": query}
    )
    
    print(f"未認證 GraphQL 請求狀態碼: {response.status_code}")
    result = response.json()
    print(f"回應: {json.dumps(result, indent=2, ensure_ascii=False)}")

def test_graphql_with_auth(token):
    """測試已認證的 GraphQL 請求"""
    print("\n=== 測試已認證的 GraphQL 請求 ===")
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # 測試查詢使用者列表
    query = """
    query {
        getUsers {
            id
            name
            email
        }
    }
    """
    
    response = requests.post(
        f"{BASE_URL}/graphql",
        json={"query": query},
        headers=headers
    )
    
    print(f"已認證 GraphQL 請求狀態碼: {response.status_code}")
    result = response.json()
    print(f"使用者列表: {json.dumps(result, indent=2, ensure_ascii=False)}")

def test_graphql_takes_with_auth(token):
    """測試查詢服藥記錄（需要認證和權限檢查）"""
    print("\n=== 測試查詢服藥記錄 ===")
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # 測試查詢自己的服藥記錄（使用者 ID 1）
    query = """
    query {
        getTakes(userId: 1, limit: 5) {
            id
            userId
            pharmaceuticalId
            quantity
            takenAt
            notes
        }
    }
    """
    
    response = requests.post(
        f"{BASE_URL}/graphql",
        json={"query": query},
        headers=headers
    )
    
    print(f"查詢自己服藥記錄狀態碼: {response.status_code}")
    result = response.json()
    print(f"服藥記錄: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 測試查詢其他人的服藥記錄（應該失敗）
    query_other = """
    query {
        getTakes(userId: 2, limit: 5) {
            id
            userId
            pharmaceuticalId
            quantity
            takenAt
            notes
        }
    }
    """
    
    response_other = requests.post(
        f"{BASE_URL}/graphql",
        json={"query": query_other},
        headers=headers
    )
    
    print(f"\n查詢他人服藥記錄狀態碼: {response_other.status_code}")
    result_other = response_other.json()
    print(f"查詢他人記錄結果: {json.dumps(result_other, indent=2, ensure_ascii=False)}")

def test_graphql_mutation_with_auth(token):
    """測試 GraphQL 變更操作"""
    print("\n=== 測試 GraphQL 變更操作 ===")
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # 測試建立服藥記錄
    mutation = """
    mutation {
        createTake(take: {
            userId: 1,
            pharmaceuticalId: 1,
            quantity: 1.0,
            takenAt: "2024-01-01T10:00:00",
            notes: "測試服藥記錄"
        }) {
            id
            userId
            pharmaceuticalId
            quantity
            takenAt
            notes
        }
    }
    """
    
    response = requests.post(
        f"{BASE_URL}/graphql",
        json={"query": mutation},
        headers=headers
    )
    
    print(f"建立服藥記錄狀態碼: {response.status_code}")
    result = response.json()
    print(f"建立結果: {json.dumps(result, indent=2, ensure_ascii=False)}")

if __name__ == "__main__":
    print("開始測試認證功能...")
    
    # 1. 測試登入
    token = test_login()
    
    # 2. 測試登入失敗
    test_login_failure()
    
    if token:
        # 3. 測試取得使用者資訊
        test_get_me(token)
        
        # 4. 測試未認證的 GraphQL 請求
        test_graphql_without_auth()
        
        # 5. 測試已認證的 GraphQL 請求
        test_graphql_with_auth(token)
        
        # 6. 測試服藥記錄查詢權限
        test_graphql_takes_with_auth(token)
        
        # 7. 測試 GraphQL 變更操作
        test_graphql_mutation_with_auth(token)
    
    print("\n認證功能測試完成！")
