#!/usr/bin/env python3
"""
測試配置模組
"""

import os
import sys
import tempfile
from pathlib import Path

import pytest
import yaml

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent.parent))

from when_did_i_take.config import find_config_file


class TestFindConfigFile:
    """測試 find_config_file 函數"""

    def test_yaml_config_found(self):
        """測試找到 YAML 配置檔案"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            config_data = {
                'DB': {
                    'host': 'localhost',
                    'port': 5432,
                    'user': 'testuser',
                    'password': 'testpass'
                }
            }

            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f)

            # 暫時改變工作目錄
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                result = find_config_file()

                assert result['host'] == 'localhost'
                assert result['port'] == '5432'
                assert result['user'] == 'testuser'
                assert result['password'] == 'testpass'
            finally:
                os.chdir(original_cwd)

    def test_ini_config_found(self):
        """測試找到 INI 配置檔案"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.ini"
            config_content = """[DB]
host = localhost
port = 5432
user = testuser
password = testpass
"""

            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(config_content)

            # 暫時改變工作目錄
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                result = find_config_file()

                assert result['host'] == 'localhost'
                assert result['port'] == '5432'
                assert result['user'] == 'testuser'
                assert result['password'] == 'testpass'
            finally:
                os.chdir(original_cwd)

    def test_yaml_priority_over_ini(self):
        """測試 YAML 檔案優先於 INI 檔案"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 建立 YAML 配置檔案
            yaml_path = Path(temp_dir) / "config.yaml"
            yaml_data = {
                'DB': {
                    'host': 'yaml_host',
                    'port': 5432,
                    'user': 'yaml_user',
                    'password': 'yaml_pass'
                }
            }
            with open(yaml_path, 'w', encoding='utf-8') as f:
                yaml.dump(yaml_data, f)

            # 建立 INI 配置檔案
            ini_path = Path(temp_dir) / "config.ini"
            ini_content = """[DB]
host = ini_host
port = 5432
user = ini_user
password = ini_pass
"""
            with open(ini_path, 'w', encoding='utf-8') as f:
                f.write(ini_content)

            # 暫時改變工作目錄
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                result = find_config_file()

                # 應該使用 YAML 的值
                assert result['host'] == 'yaml_host'
                assert result['user'] == 'yaml_user'
                assert result['password'] == 'yaml_pass'
            finally:
                os.chdir(original_cwd)

    def test_missing_required_fields(self):
        """測試缺少必要欄位時拋出錯誤"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            config_data = {
                'DB': {
                    'host': 'localhost',
                    'port': 5432,
                    # 缺少 user 和 password
                }
            }

            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f)

            # 暫時改變工作目錄
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                with pytest.raises(ValueError, match="缺少必要欄位"):
                    find_config_file()
            finally:
                os.chdir(original_cwd)

    def test_no_config_file_found(self):
        """測試找不到配置檔案時拋出錯誤"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 暫時改變工作目錄到空目錄
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                with pytest.raises(FileNotFoundError):
                    find_config_file()
            finally:
                os.chdir(original_cwd)

    def test_invalid_yaml_format(self):
        """測試無效的 YAML 格式"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"

            # 寫入無效的 YAML
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write("invalid: yaml: content: [")

            # 暫時改變工作目錄
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                with pytest.raises(Exception):  # YAML 解析錯誤
                    find_config_file()
            finally:
                os.chdir(original_cwd)

    def test_invalid_ini_format(self):
        """測試無效的 INI 格式"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.ini"

            # 寫入無效的 INI（沒有 [DB] section）
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write("host = localhost\nport = 5432")

            # 暫時改變工作目錄
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                with pytest.raises(ValueError, match="INI配置檔案格式錯誤"):
                    find_config_file()
            finally:
                os.chdir(original_cwd)
