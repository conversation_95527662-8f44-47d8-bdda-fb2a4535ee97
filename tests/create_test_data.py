#!/usr/bin/env python3
"""
建立測試資料庫和虛假資料的腳本
"""

import random
import sys
from decimal import Decimal
from pathlib import Path

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent.parent))

from faker import Faker
from sqlmodel import Session, create_engine

from when_did_i_take.models import FormulationType, Pharmaceutical, Take, User

# 設定 Faker 為繁體中文
fake = Faker('zh_TW')

# 測試資料庫路徑
TEST_DB_PATH = Path(__file__).parent / "test_data.db"


def create_test_database():
    """建立測試資料庫"""
    # 如果測試資料庫已存在，先刪除
    if TEST_DB_PATH.exists():
        TEST_DB_PATH.unlink()
        print(f"已刪除舊的測試資料庫: {TEST_DB_PATH}")

    # 建立新的測試資料庫
    database_url = f"sqlite:///{TEST_DB_PATH}"
    engine = create_engine(database_url, echo=False)

    # 建立所有資料表
    from when_did_i_take.models import SQLModel
    SQLModel.metadata.create_all(engine)

    print(f"已建立測試資料庫: {TEST_DB_PATH}")
    return engine


def create_fake_users(session: Session, count: int = 10) -> list[User]:
    """建立虛假使用者資料"""
    users = []

    for _ in range(count):
        user = User(
            name=fake.name(),
            email=fake.email(),
            created_at=fake.date_time_between(start_date='-2y', end_date='now')
        )
        session.add(user)
        users.append(user)

    session.commit()
    print(f"已建立 {count} 個虛假使用者")
    return users


def create_fake_pharmaceuticals(session: Session, count: int = 20) -> list[Pharmaceutical]:
    """建立虛假藥物資料"""
    pharmaceuticals = []

    # 常見藥物名稱
    drug_names = [
        "普拿疼", "阿斯匹靈", "布洛芬", "撲熱息痛", "維他命C",
        "維他命D", "鈣片", "魚油", "益生菌", "葉酸",
        "鐵劑", "B群", "維他命E", "葡萄糖胺", "膠原蛋白",
        "感冒糖漿", "止咳藥", "胃藥", "降血壓藥", "降血糖藥"
    ]

    # 確保藥物名稱不重複
    selected_names = fake.random_elements(elements=drug_names, length=min(count, len(drug_names)), unique=True)

    for i, name in enumerate(selected_names):
        pharmaceutical = Pharmaceutical(
            name=name,
            formulation_type=fake.random_element(elements=list(FormulationType)),
            dosage_per_unit=Decimal(str(round(random.uniform(0.5, 500.0), 2))),
            unit=fake.random_element(elements=["mg", "g", "ml", "IU", "mcg"]),
            description=fake.text(max_nb_chars=200),
            created_at=fake.date_time_between(start_date='-1y', end_date='now')
        )
        session.add(pharmaceutical)
        pharmaceuticals.append(pharmaceutical)

    session.commit()
    print(f"已建立 {len(pharmaceuticals)} 個虛假藥物")
    return pharmaceuticals


def create_fake_takes(session: Session, users: list[User], pharmaceuticals: list[Pharmaceutical], count: int = 100) -> list[Take]:
    """建立虛假服藥記錄"""
    takes = []

    for _ in range(count):
        user = fake.random_element(elements=users)
        pharmaceutical = fake.random_element(elements=pharmaceuticals)

        # 生成過去30天內的隨機時間
        taken_at = fake.date_time_between(start_date='-30d', end_date='now')

        take = Take(
            user_id=user.id,
            pharmaceutical_id=pharmaceutical.id,
            quantity=Decimal(str(round(random.uniform(0.5, 5.0), 1))),
            taken_at=taken_at,
            notes=fake.text(max_nb_chars=100) if fake.boolean(chance_of_getting_true=30) else None
        )
        session.add(take)
        takes.append(take)

    session.commit()
    print(f"已建立 {count} 個虛假服藥記錄")
    return takes


def print_database_stats(session: Session):
    """顯示資料庫統計資訊"""
    from sqlmodel import select

    user_count = len(session.exec(select(User)).all())
    pharmaceutical_count = len(session.exec(select(Pharmaceutical)).all())
    take_count = len(session.exec(select(Take)).all())

    print("\n=== 測試資料庫統計 ===")
    print(f"使用者數量: {user_count}")
    print(f"藥物數量: {pharmaceutical_count}")
    print(f"服藥記錄數量: {take_count}")
    print(f"資料庫檔案大小: {TEST_DB_PATH.stat().st_size / 1024:.2f} KB")


def main():
    """主函數"""
    print("開始建立測試資料...")

    # 建立測試資料庫
    engine = create_test_database()

    with Session(engine) as session:
        # 建立虛假資料
        users = create_fake_users(session, count=10)
        pharmaceuticals = create_fake_pharmaceuticals(session, count=15)
        takes = create_fake_takes(session, users, pharmaceuticals, count=80)

        # 顯示統計資訊
        print_database_stats(session)

    print(f"\n測試資料建立完成！資料庫位置: {TEST_DB_PATH}")


if __name__ == "__main__":
    main()
