{"tests/test_config.py::TestFindConfigFile::test_invalid_ini_format": true, "tests/test_db.py::TestDatabaseModule::test_session_context_manager": true, "tests/test_db.py::TestDatabaseModule::test_database_operations": true, "tests/test_db.py::TestDatabaseModule::test_foreign_key_relationships": true, "tests/test_db.py::TestDatabaseModule::test_unique_constraint": true, "tests/test_graphql_schema.py::TestGraphQLSchema::test_schema_creation": true, "tests/test_graphql_schema.py::TestGraphQLSchema::test_pharmaceutical_type_fields": true, "tests/test_graphql_schema.py::TestGraphQLSchema::test_take_type_fields": true, "tests/test_graphql_schema.py::TestGraphQLSchema::test_pharmaceutical_input_fields": true, "tests/test_graphql_schema.py::TestGraphQLSchema::test_take_input_fields": true, "tests/test_graphql_schema.py::TestGraphQLSchema::test_formulation_type_enum": true, "tests/test_graphql_schema.py::TestGraphQLQueries::test_query_takes": true, "tests/test_graphql_schema.py::TestGraphQLQueries::test_query_pharmaceuticals": true, "tests/test_graphql_schema.py::TestGraphQLQueries::test_query_takes_with_filters": true, "tests/test_graphql_schema.py::TestGraphQLQueries::test_query_pharmaceuticals_with_filters": true, "tests/test_models.py::TestUser::test_user_required_fields": true}