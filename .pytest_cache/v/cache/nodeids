["tests/test_config.py::TestFindConfigFile::test_ini_config_found", "tests/test_config.py::TestFindConfigFile::test_invalid_ini_format", "tests/test_config.py::TestFindConfigFile::test_invalid_yaml_format", "tests/test_config.py::TestFindConfigFile::test_missing_required_fields", "tests/test_config.py::TestFindConfigFile::test_no_config_file_found", "tests/test_config.py::TestFindConfigFile::test_yaml_config_found", "tests/test_config.py::TestFindConfigFile::test_yaml_priority_over_ini", "tests/test_db.py::TestDatabaseModule::test_create_tables", "tests/test_db.py::TestDatabaseModule::test_database_operations", "tests/test_db.py::TestDatabaseModule::test_foreign_key_relationships", "tests/test_db.py::TestDatabaseModule::test_get_database_info", "tests/test_db.py::TestDatabaseModule::test_get_engine", "tests/test_db.py::TestDatabaseModule::test_get_session", "tests/test_db.py::TestDatabaseModule::test_session_context_manager", "tests/test_db.py::TestDatabaseModule::test_unique_constraint", "tests/test_graphql_schema.py::TestGraphQLQueries::test_query_pharmaceuticals", "tests/test_graphql_schema.py::TestGraphQLQueries::test_query_pharmaceuticals_with_filters", "tests/test_graphql_schema.py::TestGraphQLQueries::test_query_takes", "tests/test_graphql_schema.py::TestGraphQLQueries::test_query_takes_with_filters", "tests/test_graphql_schema.py::TestGraphQLSchema::test_formulation_type_enum", "tests/test_graphql_schema.py::TestGraphQLSchema::test_pharmaceutical_input_fields", "tests/test_graphql_schema.py::TestGraphQLSchema::test_pharmaceutical_type_fields", "tests/test_graphql_schema.py::TestGraphQLSchema::test_schema_creation", "tests/test_graphql_schema.py::TestGraphQLSchema::test_take_input_fields", "tests/test_graphql_schema.py::TestGraphQLSchema::test_take_type_fields", "tests/test_models.py::TestFormulationType::test_formulation_type_list", "tests/test_models.py::TestFormulationType::test_formulation_type_values", "tests/test_models.py::TestModelRelationships::test_model_fields_exist", "tests/test_models.py::TestModelRelationships::test_model_table_names", "tests/test_models.py::TestPharmaceutical::test_pharmaceutical_creation", "tests/test_models.py::TestPharmaceutical::test_pharmaceutical_decimal_precision", "tests/test_models.py::TestPharmaceutical::test_pharmaceutical_formulation_types", "tests/test_models.py::TestPharmaceutical::test_pharmaceutical_required_fields", "tests/test_models.py::TestTake::test_take_creation", "tests/test_models.py::TestTake::test_take_foreign_keys", "tests/test_models.py::TestTake::test_take_quantity_precision", "tests/test_models.py::TestTake::test_take_required_fields", "tests/test_models.py::TestUser::test_user_creation", "tests/test_models.py::TestUser::test_user_optional_fields", "tests/test_models.py::TestUser::test_user_required_fields"]