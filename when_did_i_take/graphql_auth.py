"""
GraphQL 認證擴展
"""
from typing import Any, Optional

import strawberry
from fastapi import HTTPException, Request
from strawberry.extensions import Extension
from strawberry.types import Info

from .auth import get_current_user
from .models import User


class AuthenticationExtension(Extension):
    """
    GraphQL 認證擴展
    
    這個擴展會在每個 GraphQL 請求執行前檢查 JWT 權杖
    """
    
    def on_request_start(self):
        """請求開始時的處理"""
        pass
    
    def on_request_end(self):
        """請求結束時的處理"""
        pass


def get_user_from_context(info: Info) -> Optional[User]:
    """
    從 GraphQL context 中取得使用者
    
    Args:
        info: GraphQL Info 物件
        
    Returns:
        Optional[User]: 使用者物件，如果未認證則返回 None
    """
    request: Request = info.context["request"]
    
    # 從 Authorization header 取得 token
    authorization = request.headers.get("Authorization")
    if not authorization:
        return None
    
    # 檢查是否為 Bearer token
    if not authorization.startswith("Bearer "):
        return None
    
    token = authorization.split(" ")[1]
    
    try:
        user = get_current_user(token)
        return user
    except HTTPException:
        return None


def require_authentication(info: Info) -> User:
    """
    要求認證的裝飾器函數
    
    Args:
        info: GraphQL Info 物件
        
    Returns:
        User: 認證成功的使用者物件
        
    Raises:
        Exception: 認證失敗時拋出異常
    """
    user = get_user_from_context(info)
    
    if not user:
        raise Exception("需要認證才能存取此資源")
    
    return user


# 建立一個裝飾器來簡化認證檢查
def authenticated(func):
    """
    認證裝飾器
    
    用於 GraphQL resolver 函數，自動檢查使用者認證狀態
    """
    def wrapper(*args, **kwargs):
        # 從參數中找到 info 物件
        info = None
        for arg in args:
            if isinstance(arg, Info):
                info = arg
                break
        
        if not info:
            # 如果沒有找到 info，檢查 kwargs
            info = kwargs.get('info')
        
        if not info:
            raise Exception("無法找到 GraphQL Info 物件")
        
        # 檢查認證
        user = require_authentication(info)
        
        # 將使用者加入到 kwargs 中
        kwargs['current_user'] = user
        
        return func(*args, **kwargs)
    
    return wrapper


# Strawberry 權限類別
@strawberry.type
class AuthInfo:
    """認證資訊類型"""
    user_id: int
    is_authenticated: bool


class IsAuthenticated:
    """
    Strawberry 權限類別：檢查使用者是否已認證
    """
    message = "需要認證才能存取此資源"
    
    def has_permission(self, source: Any, info: Info, **kwargs) -> bool:
        """
        檢查權限
        
        Args:
            source: GraphQL source 物件
            info: GraphQL Info 物件
            **kwargs: 其他參數
            
        Returns:
            bool: 是否有權限
        """
        user = get_user_from_context(info)
        return user is not None


class IsOwner:
    """
    Strawberry 權限類別：檢查使用者是否為資源擁有者
    """
    message = "只能存取自己的資源"
    
    def __init__(self, user_id_field: str = "user_id"):
        """
        初始化
        
        Args:
            user_id_field: 用來比較的使用者 ID 欄位名稱
        """
        self.user_id_field = user_id_field
    
    def has_permission(self, source: Any, info: Info, **kwargs) -> bool:
        """
        檢查權限
        
        Args:
            source: GraphQL source 物件
            info: GraphQL Info 物件
            **kwargs: 其他參數
            
        Returns:
            bool: 是否有權限
        """
        user = get_user_from_context(info)
        if not user:
            return False
        
        # 從 kwargs 或 source 中取得要比較的 user_id
        resource_user_id = kwargs.get(self.user_id_field)
        if resource_user_id is None and hasattr(source, self.user_id_field):
            resource_user_id = getattr(source, self.user_id_field)
        
        return user.id == resource_user_id
