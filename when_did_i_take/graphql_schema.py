from datetime import datetime
from decimal import Decimal
from typing import List, Optional

import strawberry
from sqlmodel import and_, select

from .db import get_session
from .models import FormulationType, Pharmaceutical, Take, User


@strawberry.type
class UserType:
    id: int
    name: str
    email: str

    @strawberry.field
    def takes(self) -> List["TakeType"]:
        """取得此使用者的所有服藥記錄"""
        with get_session() as session:
            statement = select(Take).where(Take.user_id == self.id)
            takes = session.exec(statement).all()
            return [
                TakeType(
                    id=take.id,
                    user_id=take.user_id,
                    pharmaceutical_id=take.pharmaceutical_id,
                    quantity=take.quantity,
                    taken_at=take.taken_at,
                    notes=take.notes
                ) for take in takes
            ]


@strawberry.type
class PharmaceuticalType:
    id: int
    name: str
    formulation_type: FormulationType
    dosage_per_unit: float
    unit: str
    description: Optional[str]

    @strawberry.field
    def takes(self) -> List["TakeType"]:
        """取得此藥物的所有服藥記錄"""
        with get_session() as session:
            statement = select(Take).where(Take.pharmaceutical_id == self.id)
            takes = session.exec(statement).all()
            return [
                TakeType(
                    id=take.id,
                    user_id=take.user_id,
                    pharmaceutical_id=take.pharmaceutical_id,
                    quantity=take.quantity,
                    taken_at=take.taken_at,
                    notes=take.notes
                ) for take in takes
            ]


@strawberry.type
class TakeType:
    id: int
    user_id: int
    pharmaceutical_id: int
    quantity: float
    taken_at: datetime
    notes: Optional[str]

    @strawberry.field
    def user(self) -> Optional[UserType]:
        """取得服藥的使用者資訊"""
        with get_session() as session:
            statement = select(User).where(User.id == self.user_id)
            user = session.exec(statement).first()
            if user:
                return UserType(
                    id=user.id,
                    name=user.name,
                    email=user.email
                )
            return None

    @strawberry.field
    def pharmaceutical(self) -> Optional[PharmaceuticalType]:
        """取得服藥的藥物資訊"""
        with get_session() as session:
            statement = select(Pharmaceutical).where(Pharmaceutical.id == self.pharmaceutical_id)
            pharmaceutical = session.exec(statement).first()
            if pharmaceutical:
                return PharmaceuticalType(
                    id=pharmaceutical.id,
                    name=pharmaceutical.name,
                    formulation_type=pharmaceutical.formulation_type,
                    dosage_per_unit=pharmaceutical.dosage_per_unit,
                    unit=pharmaceutical.unit,
                    description=pharmaceutical.description
                )
            return None


@strawberry.input
class PharmaceuticalInput:
    name: str
    formulation_type: FormulationType
    dosage_per_unit: float
    unit: str
    description: Optional[str] = None


@strawberry.input
class PharmaceuticalUpdateInput:
    id: int
    name: Optional[str] = None
    formulation_type: Optional[FormulationType] = None
    dosage_per_unit: Optional[float] = None
    unit: Optional[str] = None
    description: Optional[str] = None


@strawberry.input
class TakeInput:
    user_id: int
    pharmaceutical_id: int
    quantity: float
    taken_at: datetime
    notes: Optional[str] = None


@strawberry.type
class Query:
    @strawberry.field
    def get_takes(
        self,
        user_id: int,
        pharmaceutical_id: Optional[int] = None,
        from_time: Optional[datetime] = None,
        to_time: Optional[datetime] = None,
        offset: int = 0,
        limit: int = 10
    ) -> List[TakeType]:
        with get_session() as session:
            query = select(Take).where(Take.user_id == user_id)

            if pharmaceutical_id:
                query = query.where(Take.pharmaceutical_id == pharmaceutical_id)

            if from_time and to_time:
                query = query.where(and_(Take.taken_at >= from_time, Take.taken_at <= to_time))
            elif from_time:
                query = query.where(Take.taken_at >= from_time)
            elif to_time:
                query = query.where(Take.taken_at <= to_time)

            query = query.offset(offset).limit(limit).order_by(Take.taken_at.desc())
            takes = session.exec(query).all()

            return [
                TakeType(
                    id=take.id,
                    user_id=take.user_id,
                    pharmaceutical_id=take.pharmaceutical_id,
                    quantity=float(take.quantity),
                    taken_at=take.taken_at,
                    notes=take.notes
                )
                for take in takes
            ]

    @strawberry.field
    def get_pharmaceuticals(self) -> List[PharmaceuticalType]:
        with get_session() as session:
            pharmaceuticals = session.exec(select(Pharmaceutical)).all()
            return [
                PharmaceuticalType(
                    id=pharm.id,
                    name=pharm.name,
                    formulation_type=pharm.formulation_type,
                    dosage_per_unit=float(pharm.dosage_per_unit),
                    unit=pharm.unit,
                    description=pharm.description
                )
                for pharm in pharmaceuticals
            ]

    @strawberry.field
    def get_users(self) -> List[UserType]:
        with get_session() as session:
            users = session.exec(select(User)).all()
            return [
                UserType(
                    id=user.id,
                    name=user.name,
                    email=user.email
                )
                for user in users
            ]


@strawberry.type
class Mutation:
    @strawberry.mutation
    def create_pharmaceutical(self, pharmaceutical: PharmaceuticalInput) -> PharmaceuticalType:
        with get_session() as session:
            # 檢查名稱是否重複
            existing = session.exec(
                select(Pharmaceutical).where(Pharmaceutical.name == pharmaceutical.name)
            ).first()
            if existing:
                raise ValueError(f"Pharmaceutical with name '{pharmaceutical.name}' already exists")

            new_pharmaceutical = Pharmaceutical(
                name=pharmaceutical.name,
                formulation_type=pharmaceutical.formulation_type,
                dosage_per_unit=Decimal(str(pharmaceutical.dosage_per_unit)),
                unit=pharmaceutical.unit,
                description=pharmaceutical.description
            )
            session.add(new_pharmaceutical)
            session.commit()
            session.refresh(new_pharmaceutical)

            return PharmaceuticalType(
                id=new_pharmaceutical.id,
                name=new_pharmaceutical.name,
                formulation_type=new_pharmaceutical.formulation_type,
                dosage_per_unit=float(new_pharmaceutical.dosage_per_unit),
                unit=new_pharmaceutical.unit,
                description=new_pharmaceutical.description
            )

    @strawberry.mutation
    def update_pharmaceutical(self, pharmaceutical: PharmaceuticalUpdateInput) -> PharmaceuticalType:
        with get_session() as session:
            existing = session.get(Pharmaceutical, pharmaceutical.id)
            if not existing:
                raise ValueError(f"Pharmaceutical with id {pharmaceutical.id} not found")

            # 檢查名稱是否與其他記錄重複
            if pharmaceutical.name and pharmaceutical.name != existing.name:
                name_exists = session.exec(
                    select(Pharmaceutical).where(
                        and_(
                            Pharmaceutical.name == pharmaceutical.name,
                            Pharmaceutical.id != pharmaceutical.id
                        )
                    )
                ).first()
                if name_exists:
                    raise ValueError(f"Pharmaceutical with name '{pharmaceutical.name}' already exists")

            # 更新欄位
            if pharmaceutical.name is not None:
                existing.name = pharmaceutical.name
            if pharmaceutical.formulation_type is not None:
                existing.formulation_type = pharmaceutical.formulation_type
            if pharmaceutical.dosage_per_unit is not None:
                existing.dosage_per_unit = Decimal(str(pharmaceutical.dosage_per_unit))
            if pharmaceutical.unit is not None:
                existing.unit = pharmaceutical.unit
            if pharmaceutical.description is not None:
                existing.description = pharmaceutical.description

            session.add(existing)
            session.commit()
            session.refresh(existing)

            return PharmaceuticalType(
                id=existing.id,
                name=existing.name,
                formulation_type=existing.formulation_type,
                dosage_per_unit=float(existing.dosage_per_unit),
                unit=existing.unit,
                description=existing.description
            )

    @strawberry.mutation
    def delete_pharmaceutical(self, id: int) -> bool:
        with get_session() as session:
            pharmaceutical = session.get(Pharmaceutical, id)
            if not pharmaceutical:
                return False

            session.delete(pharmaceutical)
            session.commit()
            return True

    @strawberry.mutation
    def create_take(self, take: TakeInput) -> TakeType:
        with get_session() as session:
            # 驗證 user 和 pharmaceutical 存在
            user = session.get(User, take.user_id)
            if not user:
                raise ValueError(f"User with id {take.user_id} not found")

            pharmaceutical = session.get(Pharmaceutical, take.pharmaceutical_id)
            if not pharmaceutical:
                raise ValueError(f"Pharmaceutical with id {take.pharmaceutical_id} not found")

            new_take = Take(
                user_id=take.user_id,
                pharmaceutical_id=take.pharmaceutical_id,
                quantity=Decimal(str(take.quantity)),
                taken_at=take.taken_at,
                notes=take.notes
            )
            session.add(new_take)
            session.commit()
            session.refresh(new_take)

            return TakeType(
                id=new_take.id,
                user_id=new_take.user_id,
                pharmaceutical_id=new_take.pharmaceutical_id,
                quantity=float(new_take.quantity),
                taken_at=new_take.taken_at,
                notes=new_take.notes
            )

    @strawberry.mutation
    def delete_take(self, user_id: int, pharmaceutical_id: int, take_id: int) -> bool:
        with get_session() as session:
            take = session.exec(
                select(Take).where(
                    and_(
                        Take.id == take_id,
                        Take.user_id == user_id,
                        Take.pharmaceutical_id == pharmaceutical_id
                    )
                )
            ).first()

            if not take:
                return False

            session.delete(take)
            session.commit()
            return True


schema = strawberry.Schema(query=Query, mutation=Mutation)
