from datetime import datetime
from decimal import Decimal
from typing import List, Optional

import strawberry
from sqlmodel import Session, and_, create_engine, select

from .config import find_config_file
from .models import FormulationType, Pharmaceutical, Take, User

# 從配置檔案讀取資料庫設定
try:
    db_config = find_config_file()
    # 建構資料庫連接字串 - 這裡假設使用 PostgreSQL
    # 如果需要支援其他資料庫，可以在配置檔案中添加 database_type 欄位
    database_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/when_did_i_take"
    engine = create_engine(database_url)
except FileNotFoundError:
    # 如果找不到配置檔案，使用預設的 SQLite
    print("警告: 找不到配置檔案，使用預設的 SQLite 資料庫")
    engine = create_engine("sqlite:///./test.db")


def get_session():
    return Session(engine)


@strawberry.type
class UserType:
    id: int
    name: str
    email: str


@strawberry.type
class PharmaceuticalType:
    id: int
    name: str
    ingredient: str
    serving_dosage: float
    serving_unit: str
    formulation: FormulationType


@strawberry.type
class TakeType:
    id: int
    user_id: int
    pharmaceutical_id: int
    dosage: float
    time: datetime
    memo: Optional[str]


@strawberry.input
class PharmaceuticalInput:
    name: str
    ingredient: str
    serving_dosage: float
    serving_unit: str
    formulation: FormulationType


@strawberry.input
class PharmaceuticalUpdateInput:
    id: int
    name: Optional[str] = None
    ingredient: Optional[str] = None
    serving_dosage: Optional[float] = None
    serving_unit: Optional[str] = None
    formulation: Optional[FormulationType] = None


@strawberry.input
class TakeInput:
    user_id: int
    pharmaceutical_id: int
    dosage: float
    time: datetime
    memo: Optional[str] = None


@strawberry.type
class Query:
    @strawberry.field
    def get_takes(
        self,
        user_id: int,
        pharmaceutical_id: Optional[int] = None,
        from_time: Optional[datetime] = None,
        to_time: Optional[datetime] = None,
        offset: int = 0,
        limit: int = 10
    ) -> List[TakeType]:
        with get_session() as session:
            query = select(Take).where(Take.user_id == user_id)

            if pharmaceutical_id:
                query = query.where(Take.pharmaceutical_id == pharmaceutical_id)

            if from_time and to_time:
                query = query.where(and_(Take.time >= from_time, Take.time <= to_time))
            elif from_time:
                query = query.where(Take.time >= from_time)
            elif to_time:
                query = query.where(Take.time <= to_time)

            query = query.offset(offset).limit(limit).order_by(Take.time.desc())
            takes = session.exec(query).all()

            return [
                TakeType(
                    id=take.id,
                    user_id=take.user_id,
                    pharmaceutical_id=take.pharmaceutical_id,
                    dosage=float(take.dosage),
                    time=take.time,
                    memo=take.memo
                )
                for take in takes
            ]

    @strawberry.field
    def get_pharmaceuticals(self) -> List[PharmaceuticalType]:
        with get_session() as session:
            pharmaceuticals = session.exec(select(Pharmaceutical)).all()
            return [
                PharmaceuticalType(
                    id=pharm.id,
                    name=pharm.name,
                    ingredient=pharm.ingredient,
                    serving_dosage=float(pharm.serving_dosage),
                    serving_unit=pharm.serving_unit,
                    formulation=pharm.formulation
                )
                for pharm in pharmaceuticals
            ]


@strawberry.type
class Mutation:
    @strawberry.mutation
    def create_pharmaceutical(self, pharmaceutical: PharmaceuticalInput) -> PharmaceuticalType:
        with get_session() as session:
            # 檢查名稱是否重複
            existing = session.exec(
                select(Pharmaceutical).where(Pharmaceutical.name == pharmaceutical.name)
            ).first()
            if existing:
                raise ValueError(f"Pharmaceutical with name '{pharmaceutical.name}' already exists")

            new_pharmaceutical = Pharmaceutical(
                name=pharmaceutical.name,
                ingredient=pharmaceutical.ingredient,
                serving_dosage=Decimal(str(pharmaceutical.serving_dosage)),
                serving_unit=pharmaceutical.serving_unit,
                formulation=pharmaceutical.formulation
            )
            session.add(new_pharmaceutical)
            session.commit()
            session.refresh(new_pharmaceutical)

            return PharmaceuticalType(
                id=new_pharmaceutical.id,
                name=new_pharmaceutical.name,
                ingredient=new_pharmaceutical.ingredient,
                serving_dosage=float(new_pharmaceutical.serving_dosage),
                serving_unit=new_pharmaceutical.serving_unit,
                formulation=new_pharmaceutical.formulation
            )

    @strawberry.mutation
    def update_pharmaceutical(self, pharmaceutical: PharmaceuticalUpdateInput) -> PharmaceuticalType:
        with get_session() as session:
            existing = session.get(Pharmaceutical, pharmaceutical.id)
            if not existing:
                raise ValueError(f"Pharmaceutical with id {pharmaceutical.id} not found")

            # 檢查名稱是否與其他記錄重複
            if pharmaceutical.name and pharmaceutical.name != existing.name:
                name_exists = session.exec(
                    select(Pharmaceutical).where(
                        and_(
                            Pharmaceutical.name == pharmaceutical.name,
                            Pharmaceutical.id != pharmaceutical.id
                        )
                    )
                ).first()
                if name_exists:
                    raise ValueError(f"Pharmaceutical with name '{pharmaceutical.name}' already exists")

            # 更新欄位
            if pharmaceutical.name is not None:
                existing.name = pharmaceutical.name
            if pharmaceutical.ingredient is not None:
                existing.ingredient = pharmaceutical.ingredient
            if pharmaceutical.serving_dosage is not None:
                existing.serving_dosage = Decimal(str(pharmaceutical.serving_dosage))
            if pharmaceutical.serving_unit is not None:
                existing.serving_unit = pharmaceutical.serving_unit
            if pharmaceutical.formulation is not None:
                existing.formulation = pharmaceutical.formulation

            session.add(existing)
            session.commit()
            session.refresh(existing)

            return PharmaceuticalType(
                id=existing.id,
                name=existing.name,
                ingredient=existing.ingredient,
                serving_dosage=float(existing.serving_dosage),
                serving_unit=existing.serving_unit,
                formulation=existing.formulation
            )

    @strawberry.mutation
    def delete_pharmaceutical(self, id: int) -> bool:
        with get_session() as session:
            pharmaceutical = session.get(Pharmaceutical, id)
            if not pharmaceutical:
                return False

            session.delete(pharmaceutical)
            session.commit()
            return True

    @strawberry.mutation
    def create_take(self, take: TakeInput) -> TakeType:
        with get_session() as session:
            # 驗證 user 和 pharmaceutical 存在
            user = session.get(User, take.user_id)
            if not user:
                raise ValueError(f"User with id {take.user_id} not found")

            pharmaceutical = session.get(Pharmaceutical, take.pharmaceutical_id)
            if not pharmaceutical:
                raise ValueError(f"Pharmaceutical with id {take.pharmaceutical_id} not found")

            new_take = Take(
                user_id=take.user_id,
                pharmaceutical_id=take.pharmaceutical_id,
                dosage=Decimal(str(take.dosage)),
                time=take.time,
                memo=take.memo
            )
            session.add(new_take)
            session.commit()
            session.refresh(new_take)

            return TakeType(
                id=new_take.id,
                user_id=new_take.user_id,
                pharmaceutical_id=new_take.pharmaceutical_id,
                dosage=float(new_take.dosage),
                time=new_take.time,
                memo=new_take.memo
            )

    @strawberry.mutation
    def delete_take(self, user_id: int, pharmaceutical_id: int, take_id: int) -> bool:
        with get_session() as session:
            take = session.exec(
                select(Take).where(
                    and_(
                        Take.id == take_id,
                        Take.user_id == user_id,
                        Take.pharmaceutical_id == pharmaceutical_id
                    )
                )
            ).first()

            if not take:
                return False

            session.delete(take)
            session.commit()
            return True


schema = strawberry.Schema(query=Query, mutation=Mutation)
