from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import List, Optional

from sqlmodel import Field, Relationship, SQLModel


class FormulationType(str, Enum):
    CAPSULE = "Capsule"
    PILL = "Pill"
    INJECTION = "Injection"
    POWDER = "Powder"
    SYRUP = "Syrup"
    PATCH = "Patch"
    SPRAY = "Spray"
    TOPICAL = "Topical"


class User(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(min_length=1)
    email: str = Field(min_length=1)
    created_at: Optional[datetime] = Field(default=None)

    takes: List["Take"] = Relationship(back_populates="user")


class Pharmaceutical(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(min_length=1, unique=True)
    formulation_type: FormulationType
    dosage_per_unit: Decimal = Field(decimal_places=2)
    unit: str = Field(min_length=1)
    description: Optional[str] = Field(default=None)
    created_at: Optional[datetime] = Field(default=None)

    takes: List["Take"] = Relationship(back_populates="pharmaceutical")


class Take(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    pharmaceutical_id: int = Field(foreign_key="pharmaceutical.id")
    quantity: Decimal = Field(decimal_places=2)
    taken_at: datetime
    notes: Optional[str] = Field(default=None)

    user: User = Relationship(back_populates="takes")
    pharmaceutical: Pharmaceutical = Relationship(back_populates="takes")
