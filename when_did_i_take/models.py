from sqlmodel import SQLModel, <PERSON>, Relationship
from typing import Optional, List
from decimal import Decimal
from datetime import datetime
from enum import Enum


class FormulationType(str, Enum):
    CAPSULE = "Capsule"
    PILL = "Pill"
    INJECTION = "Injection"
    POWDER = "Powder"
    SYRUP = "Syrup"
    PATCH = "Patch"
    SPRAY = "Spray"
    TOPICAL = "Topical"


class User(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    password: str = Field(min_length=1)
    name: str = Field(min_length=1)
    email: str = Field(min_length=1)
    
    takes: List["Take"] = Relationship(back_populates="user")


class Pharmaceutical(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(min_length=1)
    ingredient: str = Field(min_length=1)
    serving_dosage: Decimal = Field(decimal_places=2)
    serving_unit: str = Field(min_length=1)
    formulation: FormulationType
    
    takes: List["Take"] = Relationship(back_populates="pharmaceutical")


class Take(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    pharmaceutical_id: int = Field(foreign_key="pharmaceutical.id")
    dosage: Decimal = Field(decimal_places=2)
    time: datetime
    memo: Optional[str] = Field(default=None, max_length=20)
    
    user: User = Relationship(back_populates="takes")
    pharmaceutical: Pharmaceutical = Relationship(back_populates="takes")
