"""
JWT 認證模組
"""
from datetime import datetime, timedelta, timezone
from typing import Optional

from fastapi import HTTP<PERSON>x<PERSON>, status
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from sqlmodel import select

from .db import get_session
from .models import User

# JWT 設定
SECRET_KEY = "your-secret-key-change-this-in-production"  # 生產環境中應該使用環境變數
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 密碼加密設定
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    驗證密碼
    
    Args:
        plain_password: 明文密碼
        hashed_password: 加密後的密碼
        
    Returns:
        bool: 密碼是否正確
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    加密密碼
    
    Args:
        password: 明文密碼
        
    Returns:
        str: 加密後的密碼
    """
    return pwd_context.hash(password)


def authenticate_user(user_id: int, password: str) -> Optional[User]:
    """
    驗證使用者身份
    
    Args:
        user_id: 使用者 ID
        password: 密碼
        
    Returns:
        Optional[User]: 驗證成功返回使用者物件，否則返回 None
    """
    with get_session() as session:
        statement = select(User).where(User.id == user_id)
        user = session.exec(statement).first()
        
        if not user:
            return None
            
        if not verify_password(password, user.password_hash):
            return None
            
        return user


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    建立 JWT 存取權杖
    
    Args:
        data: 要編碼的資料
        expires_delta: 過期時間
        
    Returns:
        str: JWT 權杖
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
        
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> dict:
    """
    驗證 JWT 權杖
    
    Args:
        token: JWT 權杖
        
    Returns:
        dict: 解碼後的資料
        
    Raises:
        HTTPException: 權杖無效時拋出異常
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="無法驗證憑證",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        return {"user_id": user_id}
    except JWTError:
        raise credentials_exception


def get_current_user(token: str) -> User:
    """
    從 JWT 權杖取得當前使用者
    
    Args:
        token: JWT 權杖
        
    Returns:
        User: 使用者物件
        
    Raises:
        HTTPException: 權杖無效或使用者不存在時拋出異常
    """
    token_data = verify_token(token)
    user_id = token_data["user_id"]
    
    with get_session() as session:
        statement = select(User).where(User.id == user_id)
        user = session.exec(statement).first()
        
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="使用者不存在",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return user
