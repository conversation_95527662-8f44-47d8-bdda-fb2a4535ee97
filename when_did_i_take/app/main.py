"""
FastAPI 主應用程式
"""
from contextlib import asynccontextmanager

from fastapi import FastAPI
from strawberry.fastapi import GraphQLRouter

from ..db import create_tables
from .auth_routes import router as auth_router
from .graphql import graphql_app


@asynccontextmanager
async def lifespan(app: FastAPI):
    """應用程式生命週期管理"""
    # 啟動時建立資料表
    create_tables()
    yield
    # 關閉時的清理工作（如果需要）


# 建立 FastAPI 應用程式
app = FastAPI(
    title="When Did I Take - 用藥追蹤系統",
    description="一個用於追蹤用藥時間和劑量的 GraphQL API",
    version="0.1.0",
    lifespan=lifespan
)

# 加入認證路由
app.include_router(auth_router)

# 加入 GraphQL 路由
app.include_router(graphql_app, prefix="/graphql")


@app.get("/")
async def root():
    """根端點"""
    return {
        "message": "When Did I Take - 用藥追蹤系統",
        "version": "0.1.0",
        "auth_endpoint": "/auth/login",
        "graphql_endpoint": "/graphql",
        "graphql_playground": "/graphql"
    }


@app.get("/health")
async def health_check():
    """健康檢查端點"""
    return {"status": "healthy"}
