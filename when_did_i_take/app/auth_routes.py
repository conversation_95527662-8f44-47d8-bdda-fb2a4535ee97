"""
認證相關的 FastAPI 路由
"""
from datetime import timed<PERSON><PERSON>
from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel

from ..auth import (ACCESS_TOKEN_EXPIRE_MINUTES, authenticate_user,
                    create_access_token, get_current_user)

# 建立路由器
router = APIRouter(prefix="/auth", tags=["authentication"])

# HTTP Bearer 認證方案
security = HTTPBearer()


class LoginRequest(BaseModel):
    """登入請求模型"""
    user_id: int
    password: str


class LoginResponse(BaseModel):
    """登入回應模型"""
    access_token: str
    token_type: str
    expires_in: int


class UserInfo(BaseModel):
    """使用者資訊模型"""
    id: int
    name: str
    email: str


@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest):
    """
    使用者登入端點
    
    Args:
        login_data: 登入資料（使用者 ID 和密碼）
        
    Returns:
        LoginResponse: 包含 JWT 權杖的回應
        
    Raises:
        HTTPException: 認證失敗時拋出 401 錯誤
    """
    user = authenticate_user(login_data.user_id, login_data.password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="使用者 ID 或密碼錯誤",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )
    
    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60  # 轉換為秒
    )


@router.get("/me", response_model=UserInfo)
async def get_me(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)]
):
    """
    取得當前使用者資訊
    
    Args:
        credentials: HTTP Bearer 認證憑證
        
    Returns:
        UserInfo: 當前使用者資訊
    """
    current_user = get_current_user(credentials.credentials)
    
    return UserInfo(
        id=current_user.id,
        name=current_user.name,
        email=current_user.email
    )


# 依賴注入函數，用於需要認證的端點
async def get_current_user_dependency(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)]
):
    """
    依賴注入函數：取得當前認證使用者
    
    Args:
        credentials: HTTP Bearer 認證憑證
        
    Returns:
        User: 當前使用者物件
    """
    return get_current_user(credentials.credentials)
