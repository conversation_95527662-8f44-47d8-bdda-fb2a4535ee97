from sqlmodel import create_engine, Session, SQLModel
from typing import Optional
from .config import find_config_file


def _create_database_url(db_config: dict) -> str:
    """
    根據資料庫配置建立連線字串
    
    Args:
        db_config: 包含資料庫配置的字典
        
    Returns:
        str: 資料庫連線字串
    """
    host = db_config['host']
    port = db_config['port']
    user = db_config['user']
    password = db_config['password']
    database = db_config.get('database', 'when_did_i_take')
    
    # 建構 PostgreSQL 連線字串
    return f"postgresql://{user}:{password}@{host}:{port}/{database}"


def _initialize_database():
    """
    初始化資料庫連接
    
    Returns:
        tuple: (engine, 是否使用預設資料庫)
    """
    try:
        # 嘗試讀取配置檔案
        db_config = find_config_file()
        database_url = _create_database_url(db_config)
        engine = create_engine(database_url, echo=False)
        print(f"成功連接到資料庫: {db_config['host']}:{db_config['port']}")
        return engine, False
        
    except FileNotFoundError:
        # 找不到配置檔案，使用預設的 SQLite
        print("警告: 找不到配置檔案，使用預設的 SQLite 資料庫")
        database_url = "sqlite:///./when_did_i_take.db"
        engine = create_engine(database_url, echo=False)
        return engine, True
        
    except Exception as e:
        # 其他錯誤，降級使用 SQLite
        print(f"資料庫配置錯誤: {e}")
        print("降級使用 SQLite 資料庫")
        database_url = "sqlite:///./when_did_i_take.db"
        engine = create_engine(database_url, echo=False)
        return engine, True


def get_engine():
    """
    取得資料庫引擎
    
    Returns:
        Engine: SQLAlchemy 資料庫引擎
    """
    return _engine


def get_session() -> Session:
    """
    建立新的資料庫 session
    
    Returns:
        Session: SQLModel session 物件
    """
    return Session(_engine)


def create_tables():
    """
    建立所有資料表
    """
    from .models import User, Pharmaceutical, Take
    SQLModel.metadata.create_all(_engine)
    print("資料表建立完成")


def get_database_info() -> dict:
    """
    取得資料庫連接資訊
    
    Returns:
        dict: 包含資料庫類型和連接狀態的資訊
    """
    return {
        'engine': _engine,
        'database_type': 'sqlite' if _using_default_db else 'postgresql',
        'using_default': _using_default_db,
        'url': str(_engine.url).replace(_engine.url.password or '', '***') if _engine.url.password else str(_engine.url)
    }


# 初始化資料庫連接（模組載入時執行）
_engine, _using_default_db = _initialize_database()


# 如果直接執行此檔案，建立資料表
if __name__ == "__main__":
    print("初始化資料庫...")
    create_tables()
    
    # 顯示資料庫資訊
    db_info = get_database_info()
    print(f"資料庫類型: {db_info['database_type']}")
    print(f"連線 URL: {db_info['url']}")
    print(f"使用預設資料庫: {db_info['using_default']}")
    
    # 測試 session 建立
    try:
        with get_session() as session:
            print("Session 建立成功")
    except Exception as e:
        print(f"Session 建立失敗: {e}")
    
    print("資料庫初始化完成")
